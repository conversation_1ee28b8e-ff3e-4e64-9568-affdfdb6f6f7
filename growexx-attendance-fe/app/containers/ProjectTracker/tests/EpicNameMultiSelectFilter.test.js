/**
 * Testing the EpicNameMultiSelectFilter Component
 */

import React from 'react';
import { act, fireEvent, render, wait } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import history from 'utils/history';
import request from 'utils/request';
import { browserHistory } from 'react-router-dom';
import configureStore from '../../../configureStore';
import EpicNameMultiSelectFilter from '../EpicNameMultiSelectFilter';

let store;
jest.mock('utils/request');

const mockEpicNames = [
  'Epic 1',
  'Epic 2', 
  'Epic 3',
  'July Tasks Dhruv',
  'July Tasks Dinesh'
];

const mockParentFilterInstance = jest.fn();

const componentWrapper = (props = {}) =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <EpicNameMultiSelectFilter
            parentFilterInstance={mockParentFilterInstance}
            {...props}
          />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<EpicNameMultiSelectFilter />', () => {
  beforeEach(() => {
    store = configureStore({}, browserHistory);
    delete window.location;
    mockParentFilterInstance.mockClear();
    
    // Mock successful API response
    request.mockImplementation(() =>
      Promise.resolve({
        status: 1,
        data: {
          epicNames: mockEpicNames
        }
      })
    );
  });

  afterEach(() => {
    request.mockClear();
  });

  it('Should render the multi-select filter', async () => {
    window.location = { search: '?' };
    
    const { getByRole } = componentWrapper();
    
    // Should render the select component
    expect(getByRole('combobox')).toBeTruthy();
  });

  it('Should fetch epic names when dropdown is opened', async () => {
    window.location = { search: '?' };
    
    const { getByRole } = componentWrapper();
    
    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));
    
    await wait(() => {
      expect(request).toHaveBeenCalledWith(
        expect.stringContaining('/epic-names'),
        { method: 'GET' }
      );
    });
  });

  it('Should allow multiple epic selection without immediate API calls', async () => {
    window.location = { search: '?' };
    
    const { getByRole, getByText } = componentWrapper();
    
    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));
    
    await wait(() => {
      expect(request).toHaveBeenCalledTimes(1); // Only the initial fetch
    });

    // Select first epic
    await wait(() => {
      if (getByText('Epic 1')) {
        fireEvent.click(getByText('Epic 1'));
      }
    });

    // Select second epic
    await wait(() => {
      if (getByText('Epic 2')) {
        fireEvent.click(getByText('Epic 2'));
      }
    });

    // Should still only have the initial API call
    expect(request).toHaveBeenCalledTimes(1);
  });

  it('Should apply filter when dropdown is closed', async () => {
    window.location = { search: '?' };
    
    const { getByRole, getByText, container } = componentWrapper();
    
    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));
    
    await wait(() => {
      expect(request).toHaveBeenCalledTimes(1);
    });

    // Select multiple epics
    await wait(() => {
      if (getByText('Epic 1')) {
        fireEvent.click(getByText('Epic 1'));
      }
    });

    await wait(() => {
      if (getByText('Epic 2')) {
        fireEvent.click(getByText('Epic 2'));
      }
    });

    // Close dropdown by clicking outside
    fireEvent.click(document.body);

    await wait(() => {
      expect(mockParentFilterInstance).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  it('Should apply filter immediately when Enter is pressed', async () => {
    window.location = { search: '?' };
    
    const { getByRole, getByText } = componentWrapper();
    
    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));
    
    await wait(() => {
      expect(request).toHaveBeenCalledTimes(1);
    });

    // Select epic
    await wait(() => {
      if (getByText('Epic 1')) {
        fireEvent.click(getByText('Epic 1'));
      }
    });

    // Press Enter
    fireEvent.keyDown(getByRole('combobox'), { key: 'Enter' });

    await wait(() => {
      expect(mockParentFilterInstance).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  it('Should clear filter when all selections are removed', async () => {
    window.location = { search: '?' };
    
    const { getByRole } = componentWrapper();
    
    // Simulate clearing all selections
    fireEvent.change(getByRole('combobox'), { target: { value: [] } });

    await wait(() => {
      expect(mockParentFilterInstance).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  it('Should show correct placeholder text based on selection state', async () => {
    window.location = { search: '?' };
    
    const { getByRole, getByText } = componentWrapper();
    
    // Initially should show default placeholder
    expect(getByRole('combobox')).toHaveAttribute('placeholder', 'Select epic names...');
    
    // Open dropdown and select an epic
    fireEvent.mouseDown(getByRole('combobox'));
    
    await wait(() => {
      if (getByText('Epic 1')) {
        fireEvent.click(getByText('Epic 1'));
      }
    });

    // Should show selection count in placeholder
    await wait(() => {
      expect(getByRole('combobox')).toHaveAttribute(
        'placeholder', 
        expect.stringContaining('1 epic selected')
      );
    });
  });
});
