import React, { useState, useEffect, useCallback } from 'react';
import { Select, Tooltip } from 'antd';
import { debounce } from 'lodash';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';

const { Option } = Select;

const EpicNameMultiSelectFilter = React.forwardRef((props, ref) => {
  const [currentValue, setCurrentValue] = useState([]);
  const [epicOptions, setEpicOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [initialized, setInitialized] = useState(false);
  const [pendingValues, setPendingValues] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isApplyingFilter, setIsApplyingFilter] = useState(false);

  // Get boardId from the URL or props
  const getBoardId = () => {
    const query = new URLSearchParams(window.location.search);
    return query.get('boardId');
  };

  // Fetch epic names from API
  const fetchEpicNames = useCallback(async (search = '') => {
    const boardId = getBoardId();
    if (!boardId) return;

    setLoading(true);
    try {
      const url = `${
        API_ENDPOINTS.GET_EPIC_NAMES
      }?boardId=${boardId}&search=${encodeURIComponent(search)}`;
      const response = await request(url, { method: 'GET' });

      if (response && response.data) {
        setEpicOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching epic names:', error);
      setEpicOptions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(searchTerm => {
      fetchEpicNames(searchTerm);
    }, 500),
    [fetchEpicNames],
  );

  // Initial load of epic names and handle URL parameters
  useEffect(() => {
    const initializeFilter = async () => {
      await fetchEpicNames();

      // Check for initial values from URL parameters
      if (!initialized) {
        const query = new URLSearchParams(window.location.search);
        const epicNameParam = query.get('epicName');

        if (epicNameParam) {
          // Handle both && and comma-separated values for backward compatibility
          let initialValues = [];
          if (epicNameParam.includes('&&')) {
            initialValues = epicNameParam
              .split('&&')
              .map(name => name.trim())
              .filter(name => name);
          } else if (epicNameParam.includes(',')) {
            initialValues = epicNameParam
              .split(',')
              .map(name => name.trim())
              .filter(name => name);
          } else {
            initialValues = [epicNameParam.trim()];
          }

          if (initialValues.length > 0) {
            setCurrentValue(initialValues);
            // Apply the filter immediately for URL parameters
            applyFilter(initialValues);
          }
        }
        setInitialized(true);
      }
    };

    initializeFilter();
  }, [fetchEpicNames, initialized, props, applyFilter]);

  // Cleanup debounced functions on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
      debouncedApplyFilter.cancel();
    };
  }, [debouncedSearch, debouncedApplyFilter]);

  // Handle search input change
  const handleSearch = value => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  // Handle keyboard events
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      // Cancel debounced call and apply immediately
      debouncedApplyFilter.cancel();
      applyFilter(currentValue);
      setPendingValues([]);
      setIsDropdownOpen(false);
    }
  };

  // Apply the filter to the grid
  const applyFilter = useCallback((values) => {
    setIsApplyingFilter(true);

    if (!values || values.length === 0) {
      // Clear filter
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged(null, null);
      });
    } else {
      // Apply filter with multiple epic names joined by &&
      const filterValue = values.join('&&');
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('', filterValue);
      });
    }

    // Reset applying state after a short delay
    setTimeout(() => {
      setIsApplyingFilter(false);
    }, 500);
  }, [props]);

  // Handle selection change - use debounced filter application
  const onCustomFilterSelect = values => {
    setCurrentValue(values || []);
    setPendingValues(values || []);

    // Cancel any previous debounced calls
    debouncedApplyFilter.cancel();

    // Only apply filter with debounce if dropdown is still open
    if (isDropdownOpen) {
      debouncedApplyFilter(values || []);
    }
  };

  // Debounced apply filter function to prevent multiple API calls
  const debouncedApplyFilter = useCallback(
    debounce((values) => {
      applyFilter(values);
    }, 1000), // Wait 1 second after user stops selecting
    [applyFilter]
  );

  // Handle dropdown open/close
  const handleDropdownVisibleChange = open => {
    setIsDropdownOpen(open);

    if (open && epicOptions.length === 0) {
      fetchEpicNames();
    }

    // When dropdown closes, apply the filter immediately
    if (!open) {
      // Cancel any pending debounced calls
      debouncedApplyFilter.cancel();
      applyFilter(currentValue);
      setPendingValues([]);
    }
  };

  // Clear search when dropdown closes and apply filter
  const handleBlur = () => {
    if (searchValue) {
      setSearchValue('');
      // Reload all options when search is cleared
      fetchEpicNames();
    }
  };

  return (
    <div>
      <Tooltip
        title="Select multiple epic names. Filter will be applied when you close the dropdown or press Enter."
        placement="topLeft"
      >
        <Select
        mode="multiple"
        placeholder={isApplyingFilter ? "Applying filter..." : "Select epic names..."}
        value={currentValue}
        onChange={onCustomFilterSelect}
        onSearch={handleSearch}
        onDropdownVisibleChange={handleDropdownVisibleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        searchValue={searchValue}
        showSearch
        allowClear
        loading={loading || isApplyingFilter}
        className="epic-name-filter"
        style={{ width: '100%' }}
        maxTagCount="responsive"
        filterOption={false} // We handle filtering on server side
        notFoundContent={loading ? 'Loading...' : 'No epic names found'}
      >
        {epicOptions.map(option => (
          <Option key={option.value} value={option.value}>
            {option.text}
          </Option>
        ))}
        </Select>
      </Tooltip>
    </div>
  );
});

EpicNameMultiSelectFilter.displayName = 'EpicNameMultiSelectFilter';

export default EpicNameMultiSelectFilter;
