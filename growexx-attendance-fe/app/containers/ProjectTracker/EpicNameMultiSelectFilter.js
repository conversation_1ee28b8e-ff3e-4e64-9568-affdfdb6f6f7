import React, { useState, useEffect, useCallback } from 'react';
import { Select } from 'antd';
import { debounce } from 'lodash';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';

const { Option } = Select;

const EpicNameMultiSelectFilter = React.forwardRef((props, ref) => {
  const [currentValue, setCurrentValue] = useState([]);
  const [epicOptions, setEpicOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [initialized, setInitialized] = useState(false);

  // Get boardId from the URL or props
  const getBoardId = () => {
    const query = new URLSearchParams(window.location.search);
    return query.get('boardId');
  };

  // Fetch epic names from API
  const fetchEpicNames = useCallback(async (search = '') => {
    const boardId = getBoardId();
    if (!boardId) return;

    setLoading(true);
    try {
      const url = `${
        API_ENDPOINTS.GET_EPIC_NAMES
      }?boardId=${boardId}&search=${encodeURIComponent(search)}`;
      const response = await request(url, { method: 'GET' });

      if (response && response.data) {
        setEpicOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching epic names:', error);
      setEpicOptions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(searchTerm => {
      fetchEpicNames(searchTerm);
    }, 500),
    [fetchEpicNames],
  );

  // Initial load of epic names and handle URL parameters
  useEffect(() => {
    const initializeFilter = async () => {
      await fetchEpicNames();

      // Check for initial values from URL parameters
      if (!initialized) {
        const query = new URLSearchParams(window.location.search);
        const epicNameParam = query.get('epicName');

        if (epicNameParam) {
          // Handle both && and comma-separated values for backward compatibility
          let initialValues = [];
          if (epicNameParam.includes('&&')) {
            initialValues = epicNameParam
              .split('&&')
              .map(name => name.trim())
              .filter(name => name);
          } else if (epicNameParam.includes(',')) {
            initialValues = epicNameParam
              .split(',')
              .map(name => name.trim())
              .filter(name => name);
          } else {
            initialValues = [epicNameParam.trim()];
          }

          if (initialValues.length > 0) {
            setCurrentValue(initialValues);
            // Apply the filter
            const filterValue = initialValues.join('&&');
            props.parentFilterInstance(instance => {
              instance.onFloatingFilterChanged('', filterValue);
            });
          }
        }
        setInitialized(true);
      }
    };

    initializeFilter();
  }, [fetchEpicNames, initialized, props]);

  // Handle search input change
  const handleSearch = value => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  // Handle selection change
  const onCustomFilterSelect = values => {
    setCurrentValue(values || []);

    if (!values || values.length === 0) {
      // Clear filter
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged(null, null);
      });
    } else {
      // Apply filter with multiple epic names joined by &&
      const filterValue = values.join('&&');
      props.parentFilterInstance(instance => {
        instance.onFloatingFilterChanged('', filterValue);
      });
    }
  };

  // Handle dropdown open/close
  const handleDropdownVisibleChange = open => {
    if (open && epicOptions.length === 0) {
      fetchEpicNames();
    }
  };

  // Clear search when dropdown closes
  const handleBlur = () => {
    if (searchValue) {
      setSearchValue('');
      // Reload all options when search is cleared
      fetchEpicNames();
    }
  };

  return (
    <div>
      <Select
        mode="multiple"
        placeholder="Select epic names..."
        value={currentValue}
        onChange={onCustomFilterSelect}
        onSearch={handleSearch}
        onDropdownVisibleChange={handleDropdownVisibleChange}
        onBlur={handleBlur}
        searchValue={searchValue}
        showSearch
        allowClear
        loading={loading}
        className="epic-name-filter"
        style={{ width: '100%' }}
        maxTagCount="responsive"
        filterOption={false} // We handle filtering on server side
        notFoundContent={loading ? 'Loading...' : 'No epic names found'}
      >
        {epicOptions.map(option => (
          <Option key={option.value} value={option.value}>
            {option.text}
          </Option>
        ))}
      </Select>
    </div>
  );
});

EpicNameMultiSelectFilter.displayName = 'EpicNameMultiSelectFilter';

export default EpicNameMultiSelectFilter;
