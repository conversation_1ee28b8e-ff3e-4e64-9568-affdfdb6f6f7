import styled from 'styled-components';

export const StyledProjectTrackerMainDiv = styled.div`
  svg {
    vertical-align: baseline;
  }
  .projectTrackerHeader {
    .loader {
      display: flex;
      justify-content: center;
    }
  }
  .site-page-header {
    padding: 0px;
  }
  .download-btn {
    background-color: @primary-color;
    color: #fff;
  }
  .trigger-btn {
    background-color: @primary-color;
    color: #fff;
  }
  .cron-inprogress {
    background-color: #a1a1a1;
    padding: 5px;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td:nth-child(even) >,
  .ant-table-tbody > tr > td:nth-child(even).ant-table-cell-row-hover {
    background: inherit;
  }
  .ant-table-tbody > tr.ant-table-row:hover > td:nth-child(odd) >,
  .ant-table-tbody > tr > td:nth-child(odd).ant-table-cell-row-hover {
    background: inherit;
  }

  .odd-row,
  .odd-row > td {
    background-color: #4d186e33;
  }

  .even-row,
  .even-row > td {
    background-color: #00934533;
  }

  .tab-extra-content {
    display: flex;
    gap: 20px;
  }

  .ant-input-group-wrapper {
    width: 416px;
  }
  .ant-switch {
    width: 180px;
    height: 30px;
  }
  .ant-switch-checked .ant-switch-handle {
    left: calc(100% - 20px - 2px);
  }
  .ant-switch-handle {
    top: 5px;
    left: 4px;
  }
  .ag-paging-page-size {
    display: none;
  }
  input[class^='ag-'][type='text']:focus {
    box-shadow: none;
    border-color: #4d186e;
  }
  input[class^='ag-'][type='text']:hover {
    border-color: #4d186e;
  }
  .status-filter {
    width: 100%;
  }
  .epic-name-filter {
    width: 100%;
  }
  .epic-name-filter .ant-select-clear {
    top: 45%;
    right: 14px;
    color: #bfbfbf !important;
  }

  .tabs-table * {
    font-size: 16px;
  }
  .status-filter .ant-select-clear {
    top: 45%;
    right: 14px;
    color: #bfbfbf !important;
  }
  .deviation {
    text-align: center;
  }
  .red-deviation-bg {
    background-color: #ff7875;
  }
  .amber-deviation-bg {
    background-color: #ffbf00;
  }
  .epicStatus-bg {
    background-color: #fff;
  }
  .green-deviation-bg {
    background-color: #6ac16a;
  }
`;
